## TODO
使用python实现自动爬取页面 https://snake.timeline.ink/ 壁纸，下载到目录下
具体的分类就为这个页面下的：故纸堆、周度精选等

<img class="card" alt="故纸堆" referrerpolicy="no-referrer" id="5db473564369b288cfbeec745aa80c91" src="https://gd-hbimg.huaban.com/3a30c056b28e5f3034518e9f4ac88e1ff645a35549fd38-smyIja_fw480webp">
<img class="card" alt="周度精选" referrerpolicy="no-referrer" id="9ab9b934462a39703ca61c84c4ba0919" src="https://p.ananas.chaoxing.com/star3/400_0cQ80/35f9f23f03fd072b1a86a654f473daef.webp">

点击进去后

<img alt="故纸堆" referrerpolicy="no-referrer" id="b030dafe1dd5a9fdf0e0c8082a98a1d9" src="https://gd-hbimg.huaban.com/3a30c056b28e5f3034518e9f4ac88e1ff645a35549fd38-smyIja_fw480webp">

这个点击后实际获取的是
https://snake.timeline.ink/flip/random
这个接口的随机图片
页面会有具体的壁纸的一张壁纸

也有的会进入：
https://glutton.timeline.ink/flip/latest
这个页面有两个img，一个是预览webpp格式的，另一个才是原图。

我需要你爬取这个页面的壁纸，请注意，不要使用文档中的api，我需要实现的是爬虫功能。